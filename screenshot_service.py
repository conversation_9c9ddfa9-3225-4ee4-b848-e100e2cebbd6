import os
import time
import schedule
import random
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import <PERSON><PERSON><PERSON><PERSON>
from PIL import Image, ImageOps

# Create screenshots directory if it doesn't exist
SCREENSHOTS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'screenshots')
os.makedirs(SCREENSHOTS_DIR, exist_ok=True)

# User agents to rotate through
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
]

def get_cloudflare_bypass_options():
    """Configure Chrome options to bypass Cloudflare detection"""
    chrome_options = Options()

    # Basic stealth options
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=2560,1440')
    chrome_options.add_argument('--headless')

    # Anti-detection options
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    # Random user agent
    user_agent = random.choice(USER_AGENTS)
    chrome_options.add_argument(f'--user-agent={user_agent}')

    # Additional stealth options
    chrome_options.add_argument('--disable-extensions')
    chrome_options.add_argument('--disable-plugins')
    chrome_options.add_argument('--disable-web-security')
    chrome_options.add_argument('--allow-running-insecure-content')
    chrome_options.add_argument('--disable-background-timer-throttling')
    chrome_options.add_argument('--disable-backgrounding-occluded-windows')
    chrome_options.add_argument('--disable-renderer-backgrounding')
    chrome_options.add_argument('--disable-features=TranslateUI')
    chrome_options.add_argument('--disable-ipc-flooding-protection')
    chrome_options.add_argument('--memory-pressure-off')
    chrome_options.add_argument('--max_old_space_size=4096')

    # Additional anti-bot detection
    chrome_options.add_argument('--disable-default-apps')
    chrome_options.add_argument('--disable-sync')
    chrome_options.add_argument('--no-first-run')
    chrome_options.add_argument('--no-default-browser-check')
    chrome_options.add_argument('--disable-logging')
    chrome_options.add_argument('--disable-gpu-logging')
    chrome_options.add_argument('--silent')

    return chrome_options

def handle_cloudflare_challenge(driver, max_wait=30):
    """Handle Cloudflare challenge if present"""
    try:
        # Wait a bit for the page to load
        time.sleep(3)

        # Check for Cloudflare challenge indicators
        cloudflare_indicators = [
            "Checking your browser before accessing",
            "Please wait while we check your browser",
            "DDoS protection by Cloudflare",
            "cf-browser-verification",
            "cf-challenge-running"
        ]

        page_source = driver.page_source.lower()
        is_cloudflare_challenge = any(indicator.lower() in page_source for indicator in cloudflare_indicators)

        if is_cloudflare_challenge:
            print("Cloudflare challenge detected, waiting for automatic bypass...")

            # Wait for the challenge to complete automatically
            start_time = time.time()
            while time.time() - start_time < max_wait:
                time.sleep(2)
                current_source = driver.page_source.lower()

                # Check if we're past the challenge
                if not any(indicator.lower() in current_source for indicator in cloudflare_indicators):
                    print("Cloudflare challenge bypassed successfully!")
                    return True

                # Add some human-like mouse movements
                try:
                    actions = ActionChains(driver)
                    actions.move_by_offset(random.randint(-50, 50), random.randint(-50, 50))
                    actions.perform()
                except:
                    pass

            print(f"Cloudflare challenge still present after {max_wait} seconds")
            return False
        else:
            print("No Cloudflare challenge detected")
            return True

    except Exception as e:
        print(f"Error handling Cloudflare challenge: {str(e)}")
        return False

def invert_image_colors(image_path):
    """Invert the colors of an image to create black background with white text"""
    try:
        # Open the image
        image = Image.open(image_path)

        # Convert to RGB if not already
        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Invert the colors
        inverted_image = ImageOps.invert(image)

        # Save the inverted image
        inverted_image.save(image_path)
        print(f"Applied color inversion to: {image_path}")

    except Exception as e:
        print(f"Error inverting image colors: {str(e)}")

def take_screenshot(page_type='raiders'):
    try:
        # Get Cloudflare bypass options
        chrome_options = get_cloudflare_bypass_options()
        
        # Add retry logic for connecting to Selenium
        max_retries = 3
        retry_delay = 5  # seconds

        for attempt in range(max_retries):
            try:
                # Connect to Selenium standalone container
                driver = webdriver.Remote(
                    command_executor='http://192.:4444/wd/hub',
                    options=chrome_options
                )

                # Execute script to hide webdriver property
                driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

                print("Connected to Selenium container successfully")
                break
            except Exception as e:
                if attempt == max_retries - 1:  # Last attempt
                    raise Exception(f"Failed to connect to Selenium after {max_retries} attempts: {str(e)}")
                print(f"Connection attempt {attempt + 1} failed, retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
        
        # Add longer wait time for page load
        driver.set_page_load_timeout(30)
        
        # Navigate to the appropriate page based on page_type
        if page_type == 'tier':
            base_url = 'https://portal.uproar-guild.com'
            driver.get(f'{base_url}/tier')
            wait_element = "main.main-content .container-fluid"
            wait_by = By.CSS_SELECTOR
            filename = 'screenshot_tier.png'
            element_screenshot = True
        elif page_type == 'enchants':
            base_url = 'https://portal.uproar-guild.com'
            driver.get(f'{base_url}/enchants')
            wait_element = "main.main-content .container-fluid"
            wait_by = By.CSS_SELECTOR
            filename = 'screenshot_enchants.png'
            element_screenshot = True
        
        else:  # default to raiders
            base_url = 'https://portal.uproar-guild.com'
            driver.get(base_url)
            wait_element = "main.main-content .container-fluid"
            wait_by = By.CSS_SELECTOR
            filename = 'screenshot_raiders.png'
            element_screenshot = True

        # Handle Cloudflare challenge if present
        if not handle_cloudflare_challenge(driver):
            print("Failed to bypass Cloudflare challenge, continuing anyway...")

        # Add random delay to appear more human-like
        time.sleep(random.uniform(2, 5))

        # Wait for the element to be present with increased timeout
        try:
            element = WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((wait_by, wait_element))
            )
            print(f"Found element: {wait_element}")

            # Additional wait for element to be visible and stable
            WebDriverWait(driver, 10).until(
                EC.visibility_of_element_located((wait_by, wait_element))
            )

        except Exception as e:
            print(f"Could not find element {wait_element}, taking full page screenshot instead: {str(e)}")
            element = None
            element_screenshot = False

        # Add delay to ensure page is fully rendered and any animations complete
        time.sleep(random.uniform(3, 6))

        filepath = os.path.join(SCREENSHOTS_DIR, filename)

        # Take screenshot - either full page or specific element
        try:
            if element_screenshot and element:
                # Try element screenshot first
                element.screenshot(filepath)
                print(f"Element screenshot successful: {filepath}")
            else:
                # Fallback to full page screenshot
                driver.save_screenshot(filepath)
                print(f"Full page screenshot successful: {filepath}")
        except Exception as screenshot_error:
            print(f"Screenshot failed, trying alternative method: {str(screenshot_error)}")
            # Last resort - try full page screenshot
            driver.save_screenshot(filepath)
            print(f"Alternative screenshot method successful: {filepath}")

        

        print(f"Screenshot saved: {filepath}")

    except Exception as e:
        print(f"Error taking screenshot of {page_type} page: {str(e)}")
    
    finally:
        if 'driver' in locals():
            try:
                driver.quit()
            except:
                print("Error while closing driver")

def take_all_screenshots_with_delays():
    """Take all screenshots with random delays between them"""
    pages = ['raiders', 'tier', 'enchants', 'raid_rules']

    for i, page_type in enumerate(pages):
        print(f"Taking screenshot {i+1}/{len(pages)}: {page_type}")
        take_screenshot(page_type)

        # Add random delay between screenshots (except after the last one)
        if i < len(pages) - 1:
            delay = random.uniform(5, 15)  # 30-90 seconds between screenshots
            print(f"Waiting {delay:.1f} seconds before next screenshot...")
            time.sleep(delay)

def main():
    # Schedule the screenshot tasks to run every hour with staggered timing
    schedule.every().hour.at(":00").do(take_all_screenshots_with_delays)

    print("Screenshot service started. Taking screenshots every hour with anti-detection delays...")

    # Run the first screenshots immediately
    take_all_screenshots_with_delays()

    # Keep the script running
    while True:
        schedule.run_pending()
        time.sleep(60)  # Check every minute for pending tasks

if __name__ == "__main__":
    main() 
